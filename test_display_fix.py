#!/usr/bin/env python3
"""
Test script to verify the display utility fixes for emoji rendering issues.

This script tests the new display utility functionality and demonstrates
how it handles emoji rendering with proper fallbacks.
"""

import sys
import os
import matplotlib.pyplot as plt
import numpy as np

# Add the current directory to the path to import utils
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.display_utils import (
    configure_display, get_ranking_symbol, format_ranking_text,
    add_ranking_annotation, print_ranking_summary, diagnose_display
)

def test_emoji_handling():
    """Test emoji handling and fallback functionality."""
    print("="*60)
    print(" TESTING DISPLAY UTILITY - EMOJI HANDLING")
    print("="*60)
    
    # Configure display manager
    print("\n1. Configuring Display Manager...")
    display_manager = configure_display(
        prefer_emoji=True,
        fallback_mode='text',
        suppress_warnings=True
    )
    
    # Test ranking symbols
    print("\n2. Testing Ranking Symbols:")
    print("   Rank | Emoji Mode | Text Mode | Unicode Mode")
    print("   -----|------------|-----------|-------------")
    
    for i in range(1, 6):
        emoji_symbol = get_ranking_symbol(i, 'emoji')
        text_symbol = get_ranking_symbol(i, 'text')
        unicode_symbol = get_ranking_symbol(i, 'unicode')
        print(f"   {i:4d} | {emoji_symbol:10s} | {text_symbol:9s} | {unicode_symbol:11s}")
    
    # Test ranking text formatting
    print("\n3. Testing Ranking Text Formatting:")
    test_rankings = [
        {'rank': 1, 'model_name': 'SAITS', 'r2': 0.95, 'mae': 0.12, 'composite_score': 0.85},
        {'rank': 2, 'model_name': 'BRITS', 'r2': 0.92, 'mae': 0.15, 'composite_score': 0.88},
        {'rank': 3, 'model_name': 'Transformer', 'r2': 0.89, 'mae': 0.18, 'composite_score': 0.91}
    ]
    
    print_ranking_summary(test_rankings, "TEST MODEL RANKING")
    
    # Diagnose display issues
    print("\n4. Display Diagnostics:")
    diagnostics = diagnose_display()
    
    print(f"   Emoji Support: {diagnostics['emoji_support']}")
    print(f"   Issues Found: {len(diagnostics['issues'])}")
    
    if diagnostics['issues']:
        print("   Issues:")
        for issue in diagnostics['issues']:
            print(f"     • {issue}")
    
    if diagnostics['recommendations']:
        print("   Recommendations:")
        for rec in diagnostics['recommendations']:
            print(f"     • {rec}")
    
    font_info = diagnostics['font_info']
    print(f"   System: {font_info['system']}")
    print(f"   Current Font: {font_info['current_font']}")
    print(f"   Available Emoji Fonts: {len(font_info['available_emoji_fonts'])}")

def test_matplotlib_integration():
    """Test matplotlib integration with emoji annotations."""
    print("\n" + "="*60)
    print(" TESTING MATPLOTLIB INTEGRATION")
    print("="*60)
    
    # Create test data
    models = ['SAITS', 'BRITS', 'Transformer', 'Linear Regression']
    r2_scores = [0.95, 0.92, 0.89, 0.85]
    
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Create bar plot
    bars = ax.bar(models, r2_scores, color=['gold', 'silver', '#CD7F32', 'lightblue'])
    
    # Add ranking annotations using display utility
    print("\n1. Adding ranking annotations to matplotlib plot...")
    for i, (bar, score) in enumerate(zip(bars, r2_scores)):
        # Add score labels
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{score:.3f}', ha='center', va='bottom', fontsize=10)
        
        # Add ranking symbols using display utility
        try:
            add_ranking_annotation(ax, i + 1, 
                                 bar.get_x() + bar.get_width()/2, 
                                 bar.get_height()/2,
                                 ha='center', va='center', fontsize=14, fontweight='bold')
            print(f"   ✓ Added ranking annotation for position {i+1}")
        except Exception as e:
            print(f"   ✗ Failed to add ranking annotation for position {i+1}: {e}")
    
    ax.set_ylabel('R² Score')
    ax.set_title('Model Performance Comparison\n(with Display Utility Ranking Symbols)')
    ax.set_ylim(0, 1.0)
    ax.grid(True, alpha=0.3, axis='y')
    
    # Save the plot
    output_file = 'test_display_fix_plot.png'
    try:
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        print(f"\n2. Plot saved successfully as: {output_file}")
    except Exception as e:
        print(f"\n2. Failed to save plot: {e}")
    
    # Show the plot
    try:
        plt.show()
        print("3. Plot displayed successfully")
    except Exception as e:
        print(f"3. Failed to display plot: {e}")
    
    plt.close()

def test_different_modes():
    """Test different display modes."""
    print("\n" + "="*60)
    print(" TESTING DIFFERENT DISPLAY MODES")
    print("="*60)
    
    test_data = [
        {'rank': 1, 'model_name': 'Best Model', 'r2': 0.95, 'mae': 0.12},
        {'rank': 2, 'model_name': 'Second Model', 'r2': 0.92, 'mae': 0.15},
        {'rank': 3, 'model_name': 'Third Model', 'r2': 0.89, 'mae': 0.18}
    ]
    
    modes = ['emoji', 'text', 'unicode']
    
    for mode in modes:
        print(f"\n{mode.upper()} MODE:")
        print("-" * 20)
        
        for data in test_data:
            symbol = get_ranking_symbol(data['rank'], mode)
            formatted = format_ranking_text(data['rank'], data['model_name'], 
                                          {'r2': data['r2'], 'mae': data['mae']}, mode)
            print(f"  {formatted}")

def main():
    """Main test function."""
    print("DISPLAY UTILITY TEST SUITE")
    print("Testing emoji handling and font management fixes")
    print("This addresses the UserWarning messages about missing glyphs")
    
    try:
        # Test emoji handling
        test_emoji_handling()
        
        # Test matplotlib integration
        test_matplotlib_integration()
        
        # Test different modes
        test_different_modes()
        
        print("\n" + "="*60)
        print(" TEST SUITE COMPLETED SUCCESSFULLY")
        print("="*60)
        print("\nThe display utility should now handle emoji rendering")
        print("with proper fallbacks, eliminating the UserWarning messages.")
        print("\nKey improvements:")
        print("• Cross-platform font configuration")
        print("• Automatic emoji support detection")
        print("• Graceful fallback to text symbols")
        print("• Warning suppression for font issues")
        print("• Consistent symbol rendering across platforms")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()