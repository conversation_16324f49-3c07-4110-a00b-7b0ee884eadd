#!/usr/bin/env python3
"""
Test script for Multiple Linear Regression implementation in ML Log Prediction system.

This script tests the MLR utilities and integration with the existing pipeline
to ensure proper functionality and compatibility.
"""

import numpy as np
import pandas as pd
import sys
import warnings
warnings.filterwarnings('ignore')

def create_synthetic_well_log_data(n_samples=1000, n_wells=3, random_state=42):
    """
    Create synthetic well log data for testing MLR implementation.
    
    Returns:
        DataFrame with synthetic well log data including GR, NPHI, RHOB, and target log
    """
    np.random.seed(random_state)
    
    data_list = []
    
    for well_id in range(1, n_wells + 1):
        # Create depth values
        md_start = well_id * 1000
        md_end = md_start + n_samples
        md = np.linspace(md_start, md_end, n_samples // n_wells)
        
        # Create correlated log curves with realistic relationships
        # GR (Gamma Ray) - baseline trend
        gr = 50 + 30 * np.sin(md / 500) + np.random.normal(0, 10, len(md))
        
        # NPHI (Neutron Porosity) - inversely related to GR in some formations
        nphi = 0.25 - 0.003 * (gr - 50) + np.random.normal(0, 0.05, len(md))
        nphi = np.clip(nphi, 0, 0.5)  # Realistic porosity range
        
        # RHOB (Bulk Density) - related to porosity
        rhob = 2.65 - 1.0 * nphi + np.random.normal(0, 0.1, len(md))
        rhob = np.clip(rhob, 1.8, 2.8)  # Realistic density range
        
        # Target log (e.g., Permeability or another petrophysical property)
        # Linear combination with some non-linear effects
        target = (0.5 * gr + 100 * nphi - 50 * rhob + 
                 0.01 * gr * nphi + np.random.normal(0, 5, len(md)))
        
        # Create DataFrame for this well
        well_data = pd.DataFrame({
            'WELL': f'WELL_{well_id:02d}',
            'MD': md,
            'GR': gr,
            'NPHI': nphi,
            'RHOB': rhob,
            'TARGET': target
        })
        
        data_list.append(well_data)
    
    # Combine all wells
    combined_data = pd.concat(data_list, ignore_index=True)
    
    # Introduce some missing values in the target
    missing_indices = np.random.choice(
        combined_data.index, 
        size=int(0.2 * len(combined_data)), 
        replace=False
    )
    combined_data.loc[missing_indices, 'TARGET'] = np.nan
    
    return combined_data

def test_mlr_utilities():
    """Test the MLR utilities independently."""
    print("🧪 Testing MLR Utilities...")
    
    try:
        from mlr_utils import MLRModelWrapper, MLRPreprocessor, create_mlr_model
        
        # Create test data
        data = create_synthetic_well_log_data(n_samples=300, n_wells=2)
        
        # Prepare features and target
        feature_cols = ['GR', 'NPHI', 'RHOB', 'MD']
        target_col = 'TARGET'
        
        # Get complete cases for testing
        complete_mask = data[target_col].notna()
        X = data.loc[complete_mask, feature_cols]
        y = data.loc[complete_mask, target_col]
        
        print(f"   Test data: {len(X)} samples, {len(feature_cols)} features")
        
        # Test different MLR models
        models_to_test = ['linear', 'ridge', 'lasso', 'elastic_net']
        
        for model_type in models_to_test:
            print(f"\n   Testing {model_type} regression...")
            
            # Create model with diagnostics enabled
            model = create_mlr_model(
                model_type=model_type,
                enable_diagnostics=True,
                scaling_method='standard',
                handle_outliers=True
            )
            
            # Fit model
            model.fit(X, y)
            
            # Make predictions
            y_pred = model.predict(X)
            
            # Calculate basic metrics
            from sklearn.metrics import mean_absolute_error, r2_score
            mae = mean_absolute_error(y, y_pred)
            r2 = r2_score(y, y_pred)
            
            print(f"      ✅ {model_type.title()}: MAE={mae:.3f}, R²={r2:.3f}")
        
        print("\n   ✅ MLR utilities test completed successfully!")
        return True
        
    except Exception as e:
        print(f"   ❌ MLR utilities test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mlr_integration():
    """Test MLR integration with the main pipeline."""
    print("\n🔗 Testing MLR Integration with Main Pipeline...")
    
    try:
        from ml_core import MODEL_REGISTRY, impute_logs
        
        # Check if MLR models are in registry
        mlr_models = ['linear_regression', 'ridge_regression', 'lasso_regression', 'elastic_net']
        
        print("   Checking MODEL_REGISTRY...")
        for model_key in mlr_models:
            if model_key in MODEL_REGISTRY:
                model_config = MODEL_REGISTRY[model_key]
                print(f"      ✅ {model_config['name']} found in registry")
                
                # Test model creation
                try:
                    model_class = model_config['model_class']
                    if model_class is not None:
                        # Get default hyperparameters
                        hparams = {hp: details['default'] 
                                 for hp, details in model_config['hyperparameters'].items()}
                        
                        # Create model instance
                        model_instance = model_class(**hparams)
                        print(f"         Model instance created successfully")
                    else:
                        print(f"         ⚠️ Model class is None (MLR not available)")
                        
                except Exception as e:
                    print(f"         ❌ Model creation failed: {e}")
            else:
                print(f"      ❌ {model_key} not found in registry")
        
        # Test with synthetic data using the main imputation function
        print("\n   Testing imputation workflow...")
        data = create_synthetic_well_log_data(n_samples=600, n_wells=2)
        
        # Prepare for imputation test
        feature_cols = ['GR', 'NPHI', 'RHOB']
        target_col = 'TARGET'
        
        # Create a simple well configuration
        well_cfg = {
            'mode': 'combined',  # Use all data for training
            'training_wells': data['WELL'].unique().tolist(),
            'prediction_wells': data['WELL'].unique().tolist()
        }
        
        # Test one MLR model with the main pipeline
        if 'linear_regression' in MODEL_REGISTRY and MODEL_REGISTRY['linear_regression']['model_class'] is not None:
            print("      Testing Linear Regression with main pipeline...")
            
            # Get model configuration
            model_config = MODEL_REGISTRY['linear_regression']
            hparams = {hp: details['default'] 
                      for hp, details in model_config['hyperparameters'].items()}
            
            # Create model for testing
            model_instance = model_config['model_class'](**hparams)
            models_to_run = {'Linear Regression': model_instance}
            
            # Run imputation
            try:
                result_df, model_results = impute_logs(
                    df=data,
                    feature_cols=feature_cols,
                    target_col=target_col,
                    models_to_run=models_to_run,
                    well_cfg=well_cfg,
                    prediction_mode=1  # Standard imputation mode
                )
                
                if model_results:
                    best_result = model_results[0] if isinstance(model_results, list) else list(model_results.values())[0]
                    print(f"         ✅ Imputation successful!")
                    print(f"         Results: MAE={best_result.get('mae', 'N/A'):.3f}, R²={best_result.get('r2', 'N/A'):.3f}")
                else:
                    print(f"         ⚠️ No model results returned")
                    
            except Exception as e:
                print(f"         ❌ Imputation failed: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n   ✅ MLR integration test completed!")
        return True
        
    except Exception as e:
        print(f"   ❌ MLR integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all MLR tests."""
    print("🚀 Multiple Linear Regression Implementation Test")
    print("=" * 60)
    
    # Test utilities
    utilities_ok = test_mlr_utilities()
    
    # Test integration
    integration_ok = test_mlr_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"   MLR Utilities: {'✅ PASS' if utilities_ok else '❌ FAIL'}")
    print(f"   MLR Integration: {'✅ PASS' if integration_ok else '❌ FAIL'}")
    
    if utilities_ok and integration_ok:
        print("\n🎉 All tests passed! MLR implementation is ready for use.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
