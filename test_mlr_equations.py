#!/usr/bin/env python3
"""
Test script for Mathematical Equation Extraction and Reporting in MLR models.

This script tests the equation extraction, formatting, and reporting functionality
to ensure mathematical equations are properly displayed and integrated into reports.
"""

import numpy as np
import pandas as pd
import sys
import warnings
warnings.filterwarnings('ignore')

def create_test_well_log_data(n_samples=500, random_state=42):
    """Create realistic test well log data with known linear relationships."""
    np.random.seed(random_state)
    
    # Create depth values
    md = np.linspace(2000, 2500, n_samples)
    
    # Create realistic log curves with known relationships
    # GR (Gamma Ray) - baseline with depth trend
    gr = 60 + 0.01 * (md - 2000) + 20 * np.sin((md - 2000) / 100) + np.random.normal(0, 5, n_samples)
    
    # NPHI (Neutron Porosity) - inversely related to GR in clean formations
    nphi = 0.3 - 0.002 * (gr - 60) + np.random.normal(0, 0.03, n_samples)
    nphi = np.clip(nphi, 0.05, 0.45)
    
    # RHOB (Bulk Density) - strongly related to porosity
    rhob = 2.65 - 1.2 * nphi + np.random.normal(0, 0.05, n_samples)
    rhob = np.clip(rhob, 2.0, 2.8)
    
    # Target: DT (Delta Time/Sonic) with known linear relationship
    # DT = 55 + 0.5*GR + 200*NPHI - 30*RHOB + 0.02*MD + noise
    dt = (55 + 0.5 * gr + 200 * nphi - 30 * rhob + 0.02 * md + 
          np.random.normal(0, 3, n_samples))
    
    # Create DataFrame
    data = pd.DataFrame({
        'WELL': 'TEST_WELL_01',
        'MD': md,
        'GR': gr,
        'NPHI': nphi,
        'RHOB': rhob,
        'DT': dt
    })
    
    # Introduce some missing values in target
    missing_indices = np.random.choice(data.index, size=int(0.15 * len(data)), replace=False)
    data.loc[missing_indices, 'DT'] = np.nan
    
    return data

def test_equation_extraction():
    """Test equation extraction from MLR models."""
    print("🧪 Testing Mathematical Equation Extraction...")
    
    try:
        from mlr_utils import create_mlr_model
        
        # Create test data
        data = create_test_well_log_data()
        
        # Prepare features and target
        feature_cols = ['GR', 'NPHI', 'RHOB', 'MD']
        target_col = 'DT'
        
        # Get complete cases
        complete_mask = data[target_col].notna()
        X = data.loc[complete_mask, feature_cols]
        y = data.loc[complete_mask, target_col]
        
        print(f"   Test data: {len(X)} samples, {len(feature_cols)} features")
        print(f"   Known relationship: DT = 55 + 0.5×GR + 200×NPHI - 30×RHOB + 0.02×MD")
        
        # Test Linear Regression (most interpretable)
        print(f"\n   Testing Linear Regression equation extraction...")
        
        linear_model = create_mlr_model(
            model_type='linear',
            enable_diagnostics=True,
            scaling_method='standard'
        )
        
        # Fit model
        linear_model.fit(X, y)
        
        # Extract equation
        equation_info = linear_model.get_equation_for_reporting(target_col)
        
        if equation_info:
            print(f"\n   ✅ Equation extracted successfully!")
            print(f"   📐 Equation: {equation_info['equation_text']}")
            
            print(f"\n   📊 Coefficients:")
            for feature, coef in equation_info['coefficients'].items():
                print(f"      {feature}: {coef:.3f}")
            print(f"      Intercept: {equation_info['intercept']:.3f}")
            
            print(f"\n   💡 Interpretability: {equation_info['interpretability_note']}")
            
            return True, equation_info
        else:
            print(f"   ❌ Failed to extract equation")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Equation extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_equation_reporting():
    """Test equation integration with reporting system."""
    print(f"\n🔗 Testing Equation Integration with Reporting...")
    
    try:
        from ml_core import MODEL_REGISTRY, impute_logs
        from reporting import generate_mathematical_equation_report
        
        # Create test data
        data = create_test_well_log_data()
        
        # Prepare for imputation test
        feature_cols = ['GR', 'NPHI', 'RHOB']
        target_col = 'DT'
        
        # Simple well configuration
        well_cfg = {
            'mode': 'combined',
            'training_wells': ['TEST_WELL_01'],
            'prediction_wells': ['TEST_WELL_01']
        }
        
        # Test multiple MLR models
        mlr_models = ['linear_regression', 'ridge_regression']
        all_results = {}
        
        for model_key in mlr_models:
            if model_key in MODEL_REGISTRY and MODEL_REGISTRY[model_key]['model_class'] is not None:
                print(f"\n   Testing {model_key}...")
                
                # Get model configuration
                model_config = MODEL_REGISTRY[model_key]
                hparams = {hp: details['default'] 
                          for hp, details in model_config['hyperparameters'].items()}
                hparams['enable_diagnostics'] = False  # Reduce output for testing
                
                # Create model
                model_instance = model_config['model_class'](**hparams)
                models_to_run = {model_config['name']: model_instance}
                
                # Run imputation
                result_df, model_results = impute_logs(
                    df=data,
                    feature_cols=feature_cols,
                    target_col=target_col,
                    models_to_run=models_to_run,
                    well_cfg=well_cfg,
                    prediction_mode=1
                )
                
                all_results[model_key] = {
                    'res_df': result_df,
                    'mres': model_results
                }
                
                # Check if equation was captured
                if isinstance(model_results, dict) and 'evaluations' in model_results:
                    evaluations = model_results['evaluations']
                    if evaluations and 'mathematical_equation' in evaluations[0]:
                        print(f"      ✅ Equation captured in results")
                    else:
                        print(f"      ⚠️ No equation found in results")
                elif isinstance(model_results, list) and model_results:
                    if 'mathematical_equation' in model_results[0]:
                        print(f"      ✅ Equation captured in results")
                    else:
                        print(f"      ⚠️ No equation found in results")
        
        # Test mathematical equation report generation
        if all_results:
            print(f"\n   Testing mathematical equation report generation...")
            
            # Create a mock model_res structure for testing
            mock_model_res = {'evaluations': []}
            
            for model_key, results in all_results.items():
                mres = results['mres']
                if isinstance(mres, dict) and 'evaluations' in mres:
                    mock_model_res['evaluations'].extend(mres['evaluations'])
                elif isinstance(mres, list):
                    mock_model_res['evaluations'].extend(mres)
            
            if mock_model_res['evaluations']:
                # Generate equation report
                report_content = generate_mathematical_equation_report(
                    mock_model_res, 
                    target_col,
                    output_file='test_equation_report.txt'
                )
                
                if report_content:
                    print(f"      ✅ Mathematical equation report generated successfully")
                    return True
                else:
                    print(f"      ⚠️ Report generated but content may be empty")
                    return True
            else:
                print(f"      ⚠️ No equations found for report generation")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Equation reporting test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interpretability_comparison():
    """Test and compare interpretability of different MLR models."""
    print(f"\n📊 Testing Interpretability Comparison...")
    
    try:
        from mlr_utils import create_mlr_model
        
        # Create test data
        data = create_test_well_log_data()
        
        # Prepare features and target
        feature_cols = ['GR', 'NPHI', 'RHOB', 'MD']
        target_col = 'DT'
        
        # Get complete cases
        complete_mask = data[target_col].notna()
        X = data.loc[complete_mask, feature_cols]
        y = data.loc[complete_mask, target_col]
        
        # Test all MLR model types
        model_types = ['linear', 'ridge', 'lasso', 'elastic_net']
        interpretability_ranking = []
        
        for model_type in model_types:
            print(f"\n   Testing {model_type} regression interpretability...")
            
            # Create model
            model = create_mlr_model(
                model_type=model_type,
                enable_diagnostics=False,
                alpha=1.0 if model_type != 'linear' else None
            )
            
            # Fit model
            model.fit(X, y)
            
            # Extract equation
            equation_info = model.get_equation_for_reporting(target_col)
            
            if equation_info:
                # Calculate interpretability score (simple heuristic)
                # Linear = highest, others decrease based on regularization
                interpretability_scores = {
                    'linear': 1.0,
                    'ridge': 0.8,
                    'lasso': 0.6,
                    'elastic_net': 0.4
                }
                
                interpretability_ranking.append({
                    'model_type': model_type,
                    'equation': equation_info['equation_text'],
                    'interpretability_score': interpretability_scores[model_type],
                    'interpretability_note': equation_info['interpretability_note'],
                    'coefficients': equation_info['coefficients']
                })
                
                print(f"      ✅ Equation: {equation_info['equation_text'][:60]}...")
            else:
                print(f"      ❌ Failed to extract equation")
        
        # Display interpretability ranking
        if interpretability_ranking:
            print(f"\n   📈 Interpretability Ranking (1.0 = Most Interpretable):")
            print(f"   {'Rank':<6} {'Model':<15} {'Score':<8} {'Note'}")
            print(f"   {'-'*60}")
            
            sorted_ranking = sorted(interpretability_ranking, 
                                  key=lambda x: x['interpretability_score'], 
                                  reverse=True)
            
            for i, model_info in enumerate(sorted_ranking, 1):
                model_type = model_info['model_type']
                score = model_info['interpretability_score']
                note = model_info['interpretability_note'][:30] + "..."
                
                rank_symbol = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                print(f"   {rank_symbol:<6} {model_type:<15} {score:<8.1f} {note}")
            
            print(f"\n   🎯 Recommendation: Use Linear Regression for maximum interpretability")
            print(f"      in well log imputation when multicollinearity is not severe.")
            
            return True
        else:
            print(f"   ⚠️ No interpretability data collected")
            return False
            
    except Exception as e:
        print(f"   ❌ Interpretability comparison test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all equation extraction and reporting tests."""
    print("🚀 Mathematical Equation Extraction and Reporting Test")
    print("=" * 70)
    
    # Test equation extraction
    extraction_ok, equation_info = test_equation_extraction()
    
    # Test equation reporting
    reporting_ok = test_equation_reporting()
    
    # Test interpretability comparison
    interpretability_ok = test_interpretability_comparison()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 Test Summary:")
    print(f"   Equation Extraction: {'✅ PASS' if extraction_ok else '❌ FAIL'}")
    print(f"   Equation Reporting: {'✅ PASS' if reporting_ok else '❌ FAIL'}")
    print(f"   Interpretability Analysis: {'✅ PASS' if interpretability_ok else '❌ FAIL'}")
    
    if extraction_ok and reporting_ok and interpretability_ok:
        print("\n🎉 All tests passed! Mathematical equation functionality is ready.")
        print("\n📐 Key Findings:")
        print("   • Linear Regression provides the most interpretable equations")
        print("   • Equations are automatically extracted and displayed")
        print("   • Mathematical relationships are integrated into reports")
        print("   • Geological interpretation context is provided")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
