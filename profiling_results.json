{"system_info": {"device": "cuda", "cpu_count": 12, "memory_total": 15.79043197631836, "memory_available": 2.2932395935058594, "gpu_name": "NVIDIA GeForce RTX 2070 Super", "gpu_memory_total": 7.99969482421875, "gpu_memory_allocated": 0.0}, "data_shape": [500, 64, 4], "model_profiles": {"BRITS": {"init_time": 0.15999841690063477, "train_time": 4.082147598266602, "memory_init_mb": 103.359375, "memory_train_mb": 391.47265625, "gpu_memory_init_mb": 0.556640625, "gpu_memory_train_mb": 18.568359375, "status": "success"}, "MRNN": {"init_time": 0.0010001659393310547, "train_time": 0, "memory_init_mb": 0.00390625, "memory_train_mb": 166.83984375, "gpu_memory_init_mb": 0.0, "gpu_memory_train_mb": 35.7548828125, "status": "success"}, "Transformer": {"init_time": 0.0010013580322265625, "train_time": 0, "memory_init_mb": 0.0, "memory_train_mb": -17.03125, "gpu_memory_init_mb": 0.0, "gpu_memory_train_mb": 7.90234375, "status": "success"}}, "batch_size_tests": {"BRITS": {"16": {"processing_time": 1.0238101482391357, "gpu_memory_mb": 19.04296875, "status": "success"}, "32": {"processing_time": 1.021843433380127, "gpu_memory_mb": 19.05859375, "status": "success"}, "64": {"processing_time": 2.042844772338867, "gpu_memory_mb": 19.08984375, "status": "success"}, "128": {"processing_time": 2.03349232673645, "gpu_memory_mb": 19.15234375, "status": "success"}}, "MRNN": {"16": {"processing_time": 0, "gpu_memory_mb": 0, "status": "error: element 0 of tensors does not require grad and does not have a grad_fn"}, "32": {"processing_time": 0, "gpu_memory_mb": 0, "status": "error: element 0 of tensors does not require grad and does not have a grad_fn"}, "64": {"processing_time": 0, "gpu_memory_mb": 0, "status": "error: element 0 of tensors does not require grad and does not have a grad_fn"}, "128": {"processing_time": 0, "gpu_memory_mb": 0, "status": "error: element 0 of tensors does not require grad and does not have a grad_fn"}}, "Transformer": {"16": {"processing_time": 0, "gpu_memory_mb": 0, "status": "error: element 0 of tensors does not require grad and does not have a grad_fn"}, "32": {"processing_time": 0, "gpu_memory_mb": 0, "status": "error: element 0 of tensors does not require grad and does not have a grad_fn"}, "64": {"processing_time": 0, "gpu_memory_mb": 0, "status": "error: element 0 of tensors does not require grad and does not have a grad_fn"}, "128": {"processing_time": 0, "gpu_memory_mb": 0, "status": "error: element 0 of tensors does not require grad and does not have a grad_fn"}}}}