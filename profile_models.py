#!/usr/bin/env python3
"""
Comprehensive profiling script for BRITS, MRNN, and Transformer models
Run this first to establish baseline performance metrics
"""

import torch
import time
import psutil
import numpy as np
import gc
from typing import Dict, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from models.advanced_models.brits_model import BRITSModel
    from models.advanced_models.mrnn_model import MRNNModel
    from models.advanced_models.transformer_model import TransformerModel
    from data_handler import create_sequences, load_las_files_from_directory
    MODELS_AVAILABLE = True
except ImportError as e:
    print(f"❌ Import error: {e}")
    MODELS_AVAILABLE = False

class ModelProfiler:
    """Comprehensive model profiling utility"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.results = {}
        
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information for context"""
        info = {
            'device': str(self.device),
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total / (1024**3),  # GB
            'memory_available': psutil.virtual_memory().available / (1024**3),  # GB
        }
        
        if torch.cuda.is_available():
            info.update({
                'gpu_name': torch.cuda.get_device_name(0),
                'gpu_memory_total': torch.cuda.get_device_properties(0).total_memory / (1024**3),  # GB
                'gpu_memory_allocated': torch.cuda.memory_allocated() / (1024**3),  # GB
            })
        
        return info
    
    def create_synthetic_data(self, n_samples=1000, sequence_len=64, n_features=4) -> np.ndarray:
        """Create synthetic well log data for profiling"""
        np.random.seed(42)
        
        # Simulate realistic well log data
        data = np.random.randn(n_samples, sequence_len, n_features)
        
        # Add some realistic patterns
        for i in range(n_features):
            # Add depth trend
            trend = np.linspace(0, 1, sequence_len)
            data[:, :, i] += trend * 0.5
            
            # Add some missing values (10%)
            mask = np.random.random(data[:, :, i].shape) < 0.1
            data[:, :, i][mask] = np.nan
        
        return data.astype(np.float32)
    
    def profile_model_memory(self, model_class, model_name: str, 
                           data: np.ndarray, **model_kwargs) -> Dict[str, float]:
        """Profile memory usage during model initialization and training"""
        print(f"\n🔍 Profiling {model_name}...")
        
        try:
            # Clear memory
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # Memory before
            memory_before = psutil.Process().memory_info().rss / (1024**2)  # MB
            gpu_memory_before = 0
            if torch.cuda.is_available():
                gpu_memory_before = torch.cuda.memory_allocated() / (1024**2)  # MB
            
            # Initialize model
            start_time = time.time()
            model = model_class(
                n_features=data.shape[-1],
                sequence_len=data.shape[1],
                epochs=1,  # Single epoch for profiling
                batch_size=32,
                **model_kwargs
            )
            init_time = time.time() - start_time
            
            # Memory after initialization
            memory_after_init = psutil.Process().memory_info().rss / (1024**2)  # MB
            gpu_memory_after_init = 0
            if torch.cuda.is_available():
                gpu_memory_after_init = torch.cuda.memory_allocated() / (1024**2)  # MB
            
            # Simulate training step (if model has fit method)
                try:
                    start_time = time.time()
                    # Create a small subset for actual training
                    small_data_np = data[:min(100, len(data))]
                    small_data = torch.from_numpy(small_data_np).float().to(model.device if hasattr(model, 'device') else torch.device('cpu'))
                    
                    # This is a placeholder - adjust based on actual model interface
                    if hasattr(model, 'fit'):
                        model.fit(small_data, small_data, patience=10, min_delta=1e-4)
                    elif hasattr(model, 'train'):
                        model.train(small_data, small_data)
                    
                    train_time = time.time() - start_time
                    
                except Exception as e:
                    print(f"   Training simulation failed: {e}")
                    train_time = 0
            
            # Memory after training
            memory_after_train = psutil.Process().memory_info().rss / (1024**2)  # MB
            gpu_memory_after_train = 0
            if torch.cuda.is_available():
                gpu_memory_after_train = torch.cuda.memory_allocated() / (1024**2)  # MB
            
            return {
                'init_time': init_time,
                'train_time': train_time,
                'memory_init_mb': memory_after_init - memory_before,
                'memory_train_mb': memory_after_train - memory_after_init,
                'gpu_memory_init_mb': gpu_memory_after_init - gpu_memory_before,
                'gpu_memory_train_mb': gpu_memory_after_train - gpu_memory_after_init,
                'status': 'success'
            }
            
        except Exception as e:
            print(f"   ❌ Failed to profile {model_name}: {e}")
            return {
                'init_time': 0,
                'train_time': 0,
                'memory_init_mb': 0,
                'memory_train_mb': 0,
                'gpu_memory_init_mb': 0,
                'gpu_memory_train_mb': 0,
                'status': f'failed: {str(e)}'
            }
    
    def profile_batch_sizes(self, model_class, model_name: str, 
                          data: np.ndarray, batch_sizes=[16, 32, 64, 128]) -> Dict[int, Dict]:
        """Profile different batch sizes for optimal performance"""
        print(f"\n📊 Testing batch sizes for {model_name}...")
        
        results = {}
        for batch_size in batch_sizes:
            try:
                print(f"   Testing batch_size={batch_size}")
                
                # Clear memory
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                # Test with this batch size
                model = model_class(
                    n_features=data.shape[-1],
                    sequence_len=data.shape[1],
                    epochs=1,
                    batch_size=batch_size
                )
                
                # Time a single forward pass simulation
                start_time = time.time()
                
                # Simulate processing
                batch_data_np = data[:batch_size]
                batch_data = torch.from_numpy(batch_data_np).float().to(model.device if hasattr(model, 'device') else torch.device('cpu'))
                if hasattr(model, 'fit'):
                    model.fit(batch_data, batch_data, patience=10, min_delta=1e-4)
                
                processing_time = time.time() - start_time
                
                # Check GPU memory usage
                gpu_memory = 0
                if torch.cuda.is_available():
                    gpu_memory = torch.cuda.memory_allocated() / (1024**2)
                
                results[batch_size] = {
                    'processing_time': processing_time,
                    'gpu_memory_mb': gpu_memory,
                    'status': 'success'
                }
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    results[batch_size] = {
                        'processing_time': 0,
                        'gpu_memory_mb': 0,
                        'status': 'oom'
                    }
                else:
                    results[batch_size] = {
                        'processing_time': 0,
                        'gpu_memory_mb': 0,
                        'status': f'error: {str(e)}'
                    }
        
        return results
    
    def run_comprehensive_profiling(self) -> Dict[str, Any]:
        """Run comprehensive profiling for all models"""
        if not MODELS_AVAILABLE:
            return {"error": "Models not available for profiling"}
        
        print("🚀 Starting comprehensive model profiling...")
        
        # System information
        system_info = self.get_system_info()
        print("\n📡 System Information:")
        for key, value in system_info.items():
            print(f"   {key}: {value}")
        
        # Create synthetic data
        print("\n📈 Creating synthetic data...")
        data = self.create_synthetic_data(n_samples=500, sequence_len=64, n_features=4)
        print(f"   Data shape: {data.shape}")
        
        # Models to profile
        models = [
            (BRITSModel, "BRITS", {}),
            (MRNNModel, "MRNN", {"hidden_sizes": [64, 128, 256]}),
            (TransformerModel, "Transformer", {"d_model": 128, "n_heads": 4, "n_layers": 2})
        ]
        
        results = {
            'system_info': system_info,
            'data_shape': data.shape,
            'model_profiles': {},
            'batch_size_tests': {}
        }
        
        # Profile each model
        for model_class, name, kwargs in models:
            print(f"\n{'='*50}")
            print(f"Profiling {name}")
            print('='*50)
            
            # Basic profiling
            profile = self.profile_model_memory(model_class, name, data, **kwargs)
            results['model_profiles'][name] = profile
            
            # Batch size testing
            batch_results = self.profile_batch_sizes(model_class, name, data)
            results['batch_size_tests'][name] = batch_results
        
        return results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print a formatted summary of profiling results"""
        if 'error' in results:
            print(f"❌ Error: {results['error']}")
            return
        
        print("\n" + "="*60)
        print("📊 PROFILING SUMMARY")
        print("="*60)
        
        # Model performance summary
        print("\n📈 Model Performance:")
        for name, profile in results['model_profiles'].items():
            if profile['status'] == 'success':
                print(f"\n{name}:")
                print(f"   Init Time: {profile['init_time']:.3f}s")
                print(f"   Train Time: {profile['train_time']:.3f}s")
                print(f"   Memory Usage: {profile['memory_init_mb']:.1f} MB")
                if profile['gpu_memory_init_mb'] > 0:
                    print(f"   GPU Memory: {profile['gpu_memory_init_mb']:.1f} MB")
            else:
                print(f"\n{name}: {profile['status']}")
        
        # Optimal batch sizes
        print("\n🔍 Optimal Batch Sizes:")
        for name, batch_results in results['batch_size_tests'].items():
            print(f"\n{name}:")
            for batch_size, metrics in batch_results.items():
                if metrics['status'] == 'success':
                    print(f"   Batch {batch_size}: {metrics['processing_time']:.3f}s, "
                          f"{metrics['gpu_memory_mb']:.1f} MB")
                else:
                    print(f"   Batch {batch_size}: {metrics['status']}")

def main():
    """Main profiling function"""
    profiler = ModelProfiler()
    results = profiler.run_comprehensive_profiling()
    profiler.print_summary(results)
    
    # Save results
    import json
    with open('profiling_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print("\n📁 Results saved to profiling_results.json")

if __name__ == "__main__":
    main()