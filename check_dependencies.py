import sys
import importlib
import subprocess

def check_package(package_name):
    try:
        module = importlib.import_module(package_name)
        try:
            version = module.__version__
            return f"{package_name}: {version} (✓)"
        except AttributeError:
            return f"{package_name}: Installed but no version info (✓)"
    except ImportError:
        return f"{package_name}: Not installed (✗)"

def main():
    print("===== DEPENDENCY CHECK =====")
    print("\nChecking all required packages...\n")
    
    # Core data science packages
    print("--- Core Data Science ---")
    packages = [
        "pandas", "numpy", "scipy", "scikit-learn"
    ]
    for package in packages:
        result = check_package(package)
        print(result)
        sys.stdout.flush()
    
    # Machine learning packages
    print("\n--- Machine Learning ---")
    packages = [
        "xgboost", "lightgbm", "catboost"
    ]
    for package in packages:
        result = check_package(package)
        print(result)
        sys.stdout.flush()
    
    # Deep learning packages
    print("\n--- Deep Learning ---")
    packages = [
        "torch", "monai", "pypots", "einops"
    ]
    for package in packages:
        result = check_package(package)
        print(result)
        sys.stdout.flush()
    
    # Well log data handling
    print("\n--- Well Log Data Handling ---")
    packages = [
        "lasio"
    ]
    for package in packages:
        result = check_package(package)
        print(result)
        sys.stdout.flush()
    
    # Visualization
    print("\n--- Visualization ---")
    packages = [
        "matplotlib", "plotly", "seaborn"
    ]
    for package in packages:
        result = check_package(package)
        print(result)
        sys.stdout.flush()
    
    # Hyperparameter optimization
    print("\n--- Hyperparameter Optimization ---")
    packages = [
        "optuna"
    ]
    for package in packages:
        result = check_package(package)
        print(result)
        sys.stdout.flush()
    
    # Performance monitoring
    print("\n--- Performance Monitoring ---")
    packages = [
        "memory_profiler", "psutil"
    ]
    for package in packages:
        result = check_package(package)
        print(result)
        sys.stdout.flush()
    
    # GPU acceleration checks
    print("\n--- GPU Acceleration ---")
    sys.stdout.flush()
    
    # PyTorch CUDA check
    try:
        import torch
        print(f"PyTorch: {torch.__version__} (✓)")
        sys.stdout.flush()
        print(f"CUDA Available: {torch.cuda.is_available()}")
        sys.stdout.flush()
        if torch.cuda.is_available():
            print(f"CUDA Version: {torch.version.cuda}")
            sys.stdout.flush()
            print(f"GPU Device Count: {torch.cuda.device_count()}")
            sys.stdout.flush()
            print(f"GPU Device Name: {torch.cuda.get_device_name(0)}")
            sys.stdout.flush()
        else:
            print("CUDA: Not available (✗)")
            sys.stdout.flush()
    except ImportError:
        print("PyTorch: Not installed (✗)")
        sys.stdout.flush()
    
    print("\n--- XGBoost GPU Support ---")
    sys.stdout.flush()
    
    # XGBoost GPU check
    try:
        import xgboost as xgb
        import numpy as np
        print(f"XGBoost: {xgb.__version__} (✓)")
        sys.stdout.flush()
        
        # Create a small dataset for testing
        print("Creating test dataset...")
        sys.stdout.flush()
        data = np.random.rand(50, 10)
        label = np.random.randint(2, size=50)
        dtrain = xgb.DMatrix(data, label=label)
        
        # Try with modern GPU configuration
        print("\nTesting modern GPU configuration (device='cuda'):")
        sys.stdout.flush()
        try:
            params = {'device': 'cuda'}
            bst = xgb.train(params, dtrain, num_boost_round=1)
            print("GPU Support (Modern): Available (✓)")
            sys.stdout.flush()
        except Exception as e:
            print(f"GPU Support (Modern): Not available (✗) - {e}")
            sys.stdout.flush()
        
        # Try with deprecated GPU configuration
        print("\nTesting deprecated GPU configuration (tree_method='gpu_hist'):")
        sys.stdout.flush()
        try:
            params = {'tree_method': 'gpu_hist'}
            bst = xgb.train(params, dtrain, num_boost_round=1)
            print("GPU Support (Legacy): Available (✓)")
            sys.stdout.flush()
        except Exception as e:
            print(f"GPU Support (Legacy): Not available (✗) - {e}")
            sys.stdout.flush()
        
        # Try with CPU fallback
        print("\nTesting CPU fallback:")
        sys.stdout.flush()
        try:
            params = {'tree_method': 'hist'}
            bst = xgb.train(params, dtrain, num_boost_round=1)
            print("CPU Support: Available (✓)")
            sys.stdout.flush()
        except Exception as e:
            print(f"CPU Support: Not available (✗) - {e}")
            sys.stdout.flush()
            
    except ImportError:
        print("XGBoost: Not installed (✗)")
        sys.stdout.flush()
    except Exception as e:
        print(f"Error testing XGBoost: {e}")
        sys.stdout.flush()

if __name__ == "__main__":
    main()