#!/usr/bin/env python3
"""
Quick profiling script to assess current model performance
"""

import torch
import time
import psutil
import numpy as np
import gc

print("🔍 Quick Model Performance Assessment")
print("=" * 50)

# System information
print("\n📊 System Information:")
print(f"   CPU cores: {psutil.cpu_count()}")
print(f"   RAM total: {psutil.virtual_memory().total / (1024**3):.1f} GB")
print(f"   RAM available: {psutil.virtual_memory().available / (1024**3):.1f} GB")

if torch.cuda.is_available():
    print(f"   GPU: {torch.cuda.get_device_name(0)}")
    print(f"   GPU memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB")
    print(f"   GPU memory used: {torch.cuda.memory_allocated() / (1024**3):.1f} GB")
else:
    print("   GPU: Not available")

# Test basic operations
print("\n⚡ Performance Tests:")

# Test 1: Basic tensor operations
start = time.time()
x = torch.randn(1000, 64, 4)
if torch.cuda.is_available():
    x = x.cuda()
    torch.cuda.synchronize()
print(f"   Tensor creation (1000x64x4): {time.time() - start:.4f}s")

# Test 2: Matrix multiplication
start = time.time()
y = torch.matmul(x, x.transpose(-2, -1))
if torch.cuda.is_available():
    torch.cuda.synchronize()
print(f"   Matrix multiplication: {time.time() - start:.4f}s")

# Test 3: Memory usage
if torch.cuda.is_available():
    print(f"   GPU memory after ops: {torch.cuda.memory_allocated() / (1024**3):.2f} GB")

# Test 4: Cleanup
start = time.time()
del x, y
gc.collect()
if torch.cuda.is_available():
    torch.cuda.empty_cache()
print(f"   Cleanup time: {time.time() - start:.4f}s")

print("\n✅ Quick assessment complete!")
print("\nNext steps:")
print("1. Run detailed profiling with actual models")
print("2. Check model-specific batch sizes")
print("3. Verify GPU utilization during training")